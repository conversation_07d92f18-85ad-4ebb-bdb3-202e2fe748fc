# ============================================================================
# JAEGER TRADING SYSTEM CONFIGURATION
# ============================================================================
# This file contains all configuration parameters for the Jaeger trading system.
# Organized by functional categories for better maintainability.
# ============================================================================

# ============================================================================
# LLM (LARGE LANGUAGE MODEL) CONFIGURATION
# ============================================================================
# Settings for AI model behavior and communication

# Model selection and connection
LM_STUDIO_MODEL_SELECTION_MODE=auto
LM_STUDIO_DEFAULT_MODEL=meta-llama-3.1-8b-instruct

# Model behavior parameters
LLM_TEMPERATURE=0.1
LLM_CONTEXT_LENGTH=64000

# Learning and memory settings
LLM_MAX_LEARNING_SESSIONS=10

# Context length behavior
LLM_AUTO_ADJUST_CONTEXT=true  # Automatically use model's maximum supported context length

# ============================================================================
# BACKTESTING ENGINE CONFIGURATION
# ============================================================================
# Core backtesting parameters for strategy validation

# Account settings
DEFAULT_INITIAL_CASH=100000
DEFAULT_MARGIN=0.02

# Trading costs and execution
DEFAULT_SPREAD=0.0001
DEFAULT_COMMISSION=0.0
DEFAULT_TRADE_ON_CLOSE=true
DEFAULT_EXCLUSIVE_ORDERS=true
DEFAULT_FINALIZE_TRADES=true
DEFAULT_HEDGING=true

# Risk management defaults
DEFAULT_STOP_LOSS_PCT=2.0
DEFAULT_TAKE_PROFIT_PCT=2.0
DEFAULT_MAX_HOLDING_MINUTES=240
MAX_HOLDING_MINUTES=240

# ============================================================================
# WALK-FORWARD TESTING CONFIGURATION
# ============================================================================
# Parameters for walk-forward validation of trading strategies

WALKFORWARD_DEFAULT_CASH=10000
WALKFORWARD_DEFAULT_COMMISSION=0.0
WALKFORWARD_DEFAULT_MARGIN=1.0
WALKFORWARD_DEFAULT_EXCLUSIVE_ORDERS=true
WALKFORWARD_DEFAULT_N_SPLITS=5
WALKFORWARD_DEFAULT_GAP=0
WALKFORWARD_MIN_MULTIPLIER=1.5

# ============================================================================
# MT4 EXPERT ADVISOR CONFIGURATION
# ============================================================================
# Settings for MetaTrader 4 Expert Advisor generation and execution

# Position sizing and execution
MT4_DEFAULT_LOT_SIZE=0.1
MT4_DEFAULT_SLIPPAGE=2

# Trading time filters
MT4_DEFAULT_START_HOUR=9
MT4_DEFAULT_END_HOUR=16:30
MT4_DEFAULT_USE_TIME_FILTER=false