#!/usr/bin/env python3
"""
Test script to verify user-configurable parameters in <PERSON>ae<PERSON>
Run this to check that all configuration values are properly loaded
"""

import sys
import os
sys.path.append('src')

def test_configuration():
    """Test that all user-configurable parameters are loaded correctly"""
    try:
        from config import config
        
        print("🧪 JAEGER CONFIGURATION TEST")
        print("=" * 50)
        
        # Position Sizing Configuration
        print("\n📊 POSITION SIZING CONTROLS:")
        print(f"  Max Position Size: {config.MAX_POSITION_SIZE_PCT}%")
        print(f"  Min Position Size: {config.MIN_POSITION_SIZE_PCT}%") 
        print(f"  Default Position Size: {config.DEFAULT_POSITION_SIZE_PCT}%")
        print(f"  Max Concurrent Trades: {config.MAX_CONCURRENT_TRADES}")
        print(f"  Position Size Reduction Threshold: {config.POSITION_SIZE_REDUCTION_THRESHOLD}%")
        
        # Pattern Detection Thresholds
        print("\n🎯 PATTERN DETECTION THRESHOLDS:")
        print(f"  Range Contraction Threshold: {config.RANGE_CONTRACTION_THRESHOLD}")
        print(f"  Range Expansion Threshold: {config.RANGE_EXPANSION_THRESHOLD}")
        print(f"  Low Volatility Threshold: {config.LOW_VOLATILITY_THRESHOLD}")
        print(f"  High Volatility Threshold: {config.HIGH_VOLATILITY_THRESHOLD}")
        print(f"  Measured Move Threshold: {config.MEASURED_MOVE_THRESHOLD}")
        print(f"  Default Lookback Periods: {config.DEFAULT_LOOKBACK_PERIODS}")
        
        # Risk/Reward Configuration
        print("\n💰 RISK/REWARD CONFIGURATION:")
        print(f"  Default Risk/Reward Ratio: {config.DEFAULT_RISK_REWARD_RATIO}")
        print(f"  Default Stop Loss %: {config.DEFAULT_STOP_LOSS_PERCENTAGE}%")
        print(f"  Default Take Profit %: {config.DEFAULT_TAKE_PROFIT_PERCENTAGE}%")
        
        # LLM and Validation Settings
        print("\n🤖 LLM AND VALIDATION SETTINGS:")
        print(f"  LLM Translation Temperature: {config.LLM_TRANSLATION_TEMPERATURE}")
        print(f"  Validator Max Retries: {config.VALIDATOR_MAX_RETRIES}")
        print(f"  Validator Retry Delay: {config.VALIDATOR_RETRY_DELAY}s")
        print(f"  Quality Score Excellent Threshold: {config.QUALITY_SCORE_EXCELLENT_THRESHOLD}")
        print(f"  Quality Score Good Threshold: {config.QUALITY_SCORE_GOOD_THRESHOLD}")
        print(f"  Quality Score Fair Threshold: {config.QUALITY_SCORE_FAIR_THRESHOLD}")
        
        # Core Trading Configuration
        print("\n⚙️ CORE TRADING CONFIGURATION:")
        print(f"  Default Initial Cash: ${config.DEFAULT_INITIAL_CASH:,.0f}")
        print(f"  Default Margin: {config.DEFAULT_MARGIN}")
        print(f"  Default Spread: {config.DEFAULT_SPREAD}")
        print(f"  Default Commission: {config.DEFAULT_COMMISSION}")
        
        print("\n✅ ALL CONFIGURATION VALUES LOADED SUCCESSFULLY!")
        print("\n💡 To modify these values, edit jaeger_config.env")
        print("   Example: MAX_POSITION_SIZE_PCT=2.0")
        print("   Then restart Jaeger to apply changes.")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def show_configuration_examples():
    """Show examples of how to modify configuration"""
    print("\n📝 CONFIGURATION EXAMPLES:")
    print("=" * 50)
    
    print("\n🎯 For Conservative Trading:")
    print("MAX_POSITION_SIZE_PCT=0.5")
    print("MAX_CONCURRENT_TRADES=2") 
    print("DEFAULT_RISK_REWARD_RATIO=3.0")
    print("RANGE_CONTRACTION_THRESHOLD=0.40")
    
    print("\n🚀 For Aggressive Trading:")
    print("MAX_POSITION_SIZE_PCT=2.0")
    print("MAX_CONCURRENT_TRADES=5")
    print("DEFAULT_RISK_REWARD_RATIO=1.5")
    print("RANGE_CONTRACTION_THRESHOLD=0.20")
    
    print("\n⚡ For High-Frequency Trading:")
    print("MAX_POSITION_SIZE_PCT=0.3")
    print("MAX_CONCURRENT_TRADES=10")
    print("DEFAULT_LOOKBACK_PERIODS=10")
    print("LOW_VOLATILITY_THRESHOLD=0.0005")

if __name__ == "__main__":
    success = test_configuration()
    show_configuration_examples()
    
    if success:
        print("\n🎉 Configuration test completed successfully!")
        sys.exit(0)
    else:
        print("\n💥 Configuration test failed!")
        sys.exit(1)
