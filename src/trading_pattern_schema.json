{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Hougaard Trading Pattern Schema", "description": "Schema for LLM to output structured trading patterns compatible with backtesting.py", "type": "object", "required": ["pattern_name", "entry_conditions", "exit_conditions"], "properties": {"pattern_name": {"type": "string", "description": "Descriptive name of the trading pattern", "examples": ["Volatility Compression Breakout", "Inside Day Range Expansion", "Gap Reversal Setup"]}, "description": {"type": "string", "description": "Detailed description of the pattern and market situation it exploits"}, "market_situation": {"type": "string", "description": "Specific market conditions where this pattern occurs"}, "entry_conditions": {"type": "array", "description": "List of conditions that must be met for entry", "minItems": 1, "items": {"type": "object", "required": ["condition"], "properties": {"condition": {"type": "string", "enum": ["range_contraction", "range_expansion", "inside_day", "outside_day", "gap_up", "gap_down", "higher_high", "lower_low", "breakout_above", "breakdown_below", "low_volatility_regime", "high_volatility_regime", "volatility_expansion", "volatility_compression", "consecutive_days", "day_of_week", "hour_of_day", "measured_move", "retracement", "trend_continuation", "trend_reversal", "close_above_high", "close_below_low"]}, "periods": {"type": "integer", "description": "Number of periods/bars to look back", "minimum": 1, "maximum": 100}, "threshold": {"type": "number", "description": "Threshold value for the condition", "minimum": 0}, "lookback": {"type": "integer", "description": "Lookback period for calculations", "minimum": 1, "maximum": 252}, "type": {"type": "string", "description": "Sub-type of condition for complex patterns"}, "direction": {"type": "string", "enum": ["long", "short", "both"], "description": "Trade direction for this condition"}}, "additionalProperties": true}}, "entry_logic": {"type": "string", "enum": ["AND", "OR"], "default": "AND", "description": "Logic operator for combining entry conditions"}, "exit_conditions": {"type": "array", "description": "Conditions for exiting the trade", "minItems": 1, "items": {"type": "object", "required": ["condition"], "properties": {"condition": {"type": "string", "enum": ["fixed_stop_loss", "fixed_take_profit", "trailing_stop", "risk_reward_ratio", "time_exit", "pattern_failure", "volatility_stop", "trend_change"]}, "percentage": {"type": "number", "description": "Percentage for stop/profit (as decimal)", "minimum": 0, "maximum": 1}, "risk": {"type": "number", "description": "Risk units for risk-reward ratio", "minimum": 0.1, "maximum": 10}, "reward": {"type": "number", "description": "Reward units for risk-reward ratio", "minimum": 0.1, "maximum": 20}, "bars": {"type": "integer", "description": "Number of bars for time-based exits", "minimum": 1, "maximum": 100}}, "additionalProperties": true}}, "filters": {"type": "array", "description": "Additional filters that must be met (market regime, time, etc.)", "items": {"type": "object", "required": ["condition"], "properties": {"condition": {"type": "string", "enum": ["low_volatility_regime", "high_volatility_regime", "trend_filter", "volume_filter", "time_filter", "day_filter", "session_filter"]}, "lookback": {"type": "integer", "minimum": 1, "maximum": 252}, "threshold": {"type": "number", "minimum": 0, "maximum": 1}, "value": {"type": ["string", "number"], "description": "Filter value (day name, hour, etc.)"}}, "additionalProperties": true}}, "position_sizing": {"type": "object", "description": "Position sizing methodology", "properties": {"method": {"type": "string", "enum": ["fixed_percent", "fixed_amount", "volatility_based", "kelly_criterion"], "default": "fixed_percent"}, "value": {"type": "number", "description": "Size value (percentage for fixed_percent, dollar amount for fixed_amount)", "minimum": 0.001, "maximum": 1.0}, "max_risk": {"type": "number", "description": "Maximum risk per trade as percentage of account", "minimum": 0.001, "maximum": 0.1}}}, "statistical_edge": {"type": "object", "description": "Statistical basis for the pattern's edge", "properties": {"win_rate": {"type": "number", "description": "Expected win rate as decimal", "minimum": 0.1, "maximum": 1.0}, "avg_risk_reward": {"type": "number", "description": "Average risk-reward ratio", "minimum": 0.1, "maximum": 10.0}, "sample_size": {"type": "integer", "description": "Number of historical occurrences analyzed", "minimum": 20}, "confidence_level": {"type": "number", "description": "Statistical confidence level", "minimum": 0.5, "maximum": 0.99}}}, "optimal_conditions": {"type": "object", "description": "Optimal market conditions for pattern effectiveness", "properties": {"market_regime": {"type": "array", "items": {"type": "string", "enum": ["trending", "ranging", "volatile", "quiet", "bullish", "bearish"]}}, "timeframes": {"type": "array", "items": {"type": "string", "enum": ["1m", "5m", "15m", "1h", "4h", "1d", "1w"]}}, "sessions": {"type": "array", "items": {"type": "string", "enum": ["asian", "london", "new_york", "overlap"]}}}}, "behavioral_logic": {"type": "string", "description": "Explanation of the behavioral/psychological basis for why this pattern works"}, "implementation_notes": {"type": "string", "description": "Additional notes for proper implementation and discretionary judgment"}}}