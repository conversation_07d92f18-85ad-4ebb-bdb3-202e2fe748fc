#!/usr/bin/env python3
"""
🎯 PATTERN TRANSLATION PROMPTS - STAGE 2 OF TWO-STAGE DISCOVERY SYSTEM

This module implements Stage 2: Translation of sophisticated patterns discovered in Stage 1
into backtesting-compatible JSON schema format that can be parsed and validated by the existing pipeline.

Stage 1: <PERSON> → Stage 2: JSON Schema Translation → Validation Pipeline
"""

import json

class PatternTranslationPrompts:
    """Generate prompts to translate sophisticated patterns into backtesting-compatible format"""
    
    @staticmethod
    def get_backtesting_constraints():
        """Get the exact constraints required for JSON schema backtesting compatibility"""
        return {
            "entry_conditions": [
                "close_above_high", "close_below_low", "range_expansion", "range_contraction",
                "inside_day", "outside_day", "gap_up", "gap_down", "higher_high", "lower_low",
                "breakout_above", "breakdown_below", "low_volatility_regime", "high_volatility_regime",
                "volatility_expansion", "volatility_compression", "consecutive_days"
            ],
            "exit_conditions": [
                "risk_reward_ratio", "fixed_stop_loss", "fixed_take_profit", "trailing_stop",
                "time_exit", "pattern_failure", "volatility_stop", "trend_change"
            ],
            "entry_logic_options": ["AND", "OR"],
            "position_sizing_methods": ["fixed_percent", "fixed_amount", "volatility_based", "kelly_criterion"],
            "timeframe_options": ["1m", "5m", "15m", "1h", "4h", "1d", "1w"],
            "market_regimes": ["trending", "ranging", "volatile", "quiet", "bullish", "bearish"],
            "sessions": ["asian", "london", "new_york", "overlap"]
        }
    
    @staticmethod
    def get_translation_principles():
        """Core principles for translating sophisticated patterns to simple rules"""
        return [
            "Preserve the core behavioral insight while simplifying execution logic",
            "Convert complex multi-condition patterns into the most essential trigger condition",
            "Maintain the statistical edge by focusing on the primary pattern driver",
            "Ensure the simplified pattern still exploits the same behavioral inefficiency",
            "Choose the backtesting format that best captures the pattern's essence",
            "Prioritize patterns that can be effectively simplified without losing their edge",
            "If a pattern cannot be simplified without losing its core logic, note this limitation"
        ]
    
    @staticmethod
    def get_translation_examples():
        """Examples of sophisticated pattern translation to JSON schema format"""
        return [
            {
                "sophisticated": "When volatility contracts after 3 consecutive higher closes, and the 4th day opens below previous close but recovers above the 2nd day's high within 2 hours, participants typically drive price to test recent highs",
                "json_output": {
                    "pattern_name": "Volatility Compression Recovery Breakout",
                    "description": "Exploits institutional FOMO and retail momentum after volatility compression",
                    "market_situation": "Low volatility environment with consecutive higher closes followed by recovery",
                    "entry_conditions": [
                        {
                            "condition": "consecutive_days",
                            "type": "range_contraction",
                            "periods": 3,
                            "threshold": 0.20
                        },
                        {
                            "condition": "close_above_high",
                            "lookback": 2
                        }
                    ],
                    "entry_logic": "AND",
                    "exit_conditions": [
                        {
                            "condition": "risk_reward_ratio",
                            "risk": 1,
                            "reward": 2.5
                        }
                    ],
                    "behavioral_logic": "Volatility compression followed by recovery triggers institutional FOMO and retail momentum"
                }
            },
            {
                "sophisticated": "During London-NY overlap, when institutional flows create specific price behaviors where the market makes a new session high but fails to hold above the previous day's close, retail participants typically panic-sell",
                "json_output": {
                    "pattern_name": "Failed Breakout Panic Sell",
                    "description": "Exploits retail panic after failed breakout during high-volume session",
                    "market_situation": "High-volume session with failed breakout above previous close",
                    "entry_conditions": [
                        {
                            "condition": "close_below_low",
                            "lookback": 1
                        }
                    ],
                    "exit_conditions": [
                        {
                            "condition": "risk_reward_ratio",
                            "risk": 1,
                            "reward": 1.5
                        }
                    ],
                    "filters": [
                        {
                            "condition": "session_filter",
                            "value": "overlap"
                        }
                    ],
                    "behavioral_logic": "Failed breakout above previous close during high-volume session triggers retail panic"
                }
            }
        ]
    
    @staticmethod
    def generate_stage2_translation_prompt(sophisticated_patterns):
        """
        Generate Stage 2: Pattern Translation Prompt
        
        This prompt takes sophisticated patterns from Stage 1 and translates them
        into backtesting-compatible format while preserving their core insights.
        
        Args:
            sophisticated_patterns: The output from Stage 1 discovery
            
        Returns:
            Stage 2 translation prompt string
        """
        
        constraints = PatternTranslationPrompts.get_backtesting_constraints()
        principles = PatternTranslationPrompts.get_translation_principles()
        examples = PatternTranslationPrompts.get_translation_examples()
        
        prompt = f"""🎯 STAGE 2: PATTERN TRANSLATION TO BACKTESTING FORMAT

You have successfully discovered sophisticated trading patterns using Tom Hougaard's methodology. Now you must translate these patterns into backtesting-compatible format while preserving their core behavioral insights.

📊 SOPHISTICATED PATTERNS DISCOVERED IN STAGE 1:
{sophisticated_patterns}

🔧 TRANSLATION MISSION:
Convert each sophisticated pattern above into simple backtesting-compatible rules that can be tested and validated. Your goal is to preserve the core behavioral insight while simplifying the execution logic.

🎯 TRANSLATION PRINCIPLES:
{chr(10).join([f'• {principle}' for principle in principles])}

🚨 JSON SCHEMA CONSTRAINTS - MANDATORY FORMAT:

**Entry Condition Types - Use ONLY these:**
{chr(10).join([f'• {condition}' for condition in constraints['entry_conditions']])}

**Exit Condition Types - Use ONLY these:**
{chr(10).join([f'• {condition}' for condition in constraints['exit_conditions']])}

**Entry Logic Options:** {', '.join(constraints['entry_logic_options'])}
**Position Sizing Methods:** {', '.join(constraints['position_sizing_methods'])}
**Timeframe Options:** {', '.join(constraints['timeframe_options'])}
**Market Regimes:** {', '.join(constraints['market_regimes'])}
**Sessions:** {', '.join(constraints['sessions'])}

🌟 JSON TRANSLATION EXAMPLES:

Example 1:
Sophisticated Pattern: "{examples[0]['sophisticated']}"
JSON Output:
```json
{json.dumps(examples[0]['json_output'], indent=2)}
```

Example 2:
Sophisticated Pattern: "{examples[1]['sophisticated']}"
JSON Output:
```json
{json.dumps(examples[1]['json_output'], indent=2)}
```

🎯 REQUIRED TRANSLATION OUTPUT FORMAT - JSON SCHEMA:

You MUST output valid JSON using the following schema structure. For each sophisticated pattern from Stage 1, provide a JSON object or array of objects:

```json
{{
  "pattern_name": "[Same Name from Stage 1]",
  "description": "[Detailed description of the pattern and market situation it exploits]",
  "market_situation": "[Specific market conditions where this pattern occurs]",
  "entry_conditions": [
    {{
      "condition": "[condition_type from approved list]",
      "threshold": "[numeric_value_if_applicable]",
      "lookback": "[periods_to_look_back]",
      "periods": "[number_of_periods_if_applicable]"
    }}
  ],
  "entry_logic": "AND",
  "exit_conditions": [
    {{
      "condition": "risk_reward_ratio",
      "risk": 1,
      "reward": "[reward_multiplier_minimum_2]"
    }}
  ],
  "position_sizing": {{
    "method": "fixed_percent",
    "value": "[percentage_as_decimal]",
    "max_risk": "[max_risk_as_decimal]"
  }},
  "behavioral_logic": "[The essential behavioral inefficiency this pattern exploits]",
  "implementation_notes": "[How you simplified the complex pattern while preserving its edge]"
}}
```

🚨 CRITICAL JSON TRANSLATION REQUIREMENTS:

• Output MUST be valid JSON that can be parsed by json.loads()
• Use ONLY the condition types from the approved lists above
• Include ALL required fields: pattern_name, entry_conditions, exit_conditions
• Preserve the core behavioral insight in the behavioral_logic field
• Ensure each translated pattern still has a clear statistical edge
• Use appropriate thresholds, lookback periods, and parameters for conditions
• If a pattern cannot be effectively translated to JSON schema, explain why in implementation_notes

🎯 PROFITABILITY REQUIREMENTS:
• Profit target MUST be at least 2:1 risk-reward minimum (preferably 3:1)
• Pattern must overcome 1-pip spread cost PLUS generate substantial profits
• Focus ONLY on HIGH-PROBABILITY setups with clear statistical edges
• Expected win rate >60% OR risk-reward ratio >2:1

Begin translating your sophisticated patterns into backtesting-compatible format now:"""

        return prompt
    
    @staticmethod
    def validate_translation_output(translation_output):
        """
        Validate that the translation output meets backtesting requirements
        
        Args:
            translation_output: The LLM's translation output
            
        Returns:
            Dict with validation results
        """
        constraints = PatternTranslationPrompts.get_backtesting_constraints()
        
        validation_results = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Check for required entry conditions
        valid_entry_found = False
        for entry_condition in constraints['entry_conditions']:
            if entry_condition in translation_output.lower():
                valid_entry_found = True
                break

        if not valid_entry_found:
            validation_results['warnings'].append("No recognized entry condition found")

        # Check for required exit conditions
        valid_exit_found = False
        for exit_condition in constraints['exit_conditions']:
            if exit_condition in translation_output.lower():
                valid_exit_found = True
                break

        if not valid_exit_found:
            validation_results['warnings'].append("No recognized exit condition found")

        # Check for position sizing method
        sizing_found = False
        for sizing_method in constraints['position_sizing_methods']:
            if sizing_method in translation_output.lower():
                sizing_found = True
                break

        if not sizing_found:
            validation_results['warnings'].append("No recognized position sizing method found")
        
        return validation_results
