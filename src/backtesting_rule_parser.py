#!/usr/bin/env python3
"""
🎯 SCHEMA-BASED BACKTESTING RULE PARSER

Parses structured JSON trading patterns from LLM and converts them into executable backtesting functions.
Supports <PERSON>'s situational analysis methodology with robust schema-based pattern processing.

Key Features:
- Parses structured JSON patterns (no more regex hell!)
- Generates Python functions compatible with walk-forward validation
- Handles complex multi-condition entry/exit logic
- Validates patterns against JSON schema
- Extensible condition evaluation system
"""

import json
import re
from dataclasses import dataclass, field
from typing import List, Optional, Callable, Dict, Any
import pandas as pd

# Optional config import for testing compatibility
try:
    from config import config
    WALKFORWARD_MIN_MULTIPLIER = config.WALKFORWARD_MIN_MULTIPLIER
except ImportError:
    # Default value for testing
    WALKFORWARD_MIN_MULTIPLIER = 1.5


class BacktestingRuleParseError(Exception):
    """Exception raised when backtesting rule parsing fails"""
    pass


@dataclass
class TradingPattern:
    """Represents a structured trading pattern from JSON schema"""
    pattern_name: str
    entry_conditions: List[Dict[str, Any]]
    exit_conditions: List[Dict[str, Any]]
    entry_logic: str = "AND"
    filters: List[Dict[str, Any]] = field(default_factory=list)
    position_sizing: Dict[str, Any] = field(default_factory=lambda: {"method": "fixed_percent", "value": config.DEFAULT_POSITION_SIZE_PCT / 100})
    description: str = ""
    market_situation: str = ""
    behavioral_logic: str = ""
    statistical_edge: Dict[str, Any] = field(default_factory=dict)
    optimal_conditions: Dict[str, Any] = field(default_factory=dict)
    implementation_notes: str = ""

    # Backward compatibility properties
    @property
    def rule_id(self) -> int:
        """Generate rule_id for backward compatibility"""
        return hash(self.pattern_name) % 10000

    @property
    def name(self) -> str:
        """Alias for pattern_name for backward compatibility"""
        return self.pattern_name

    @property
    def direction(self) -> str:
        """Extract direction from entry conditions for backward compatibility"""
        # Detect direction from entry conditions
        for condition in self.entry_conditions:
            condition_type = condition.get('condition', '')
            if condition_type in ['close_below_low', 'breakdown_below', 'gap_down', 'lower_low']:
                return "short"
            elif condition_type in ['close_above_high', 'breakout_above', 'gap_up', 'higher_high']:
                return "long"

        # Default to long if no clear direction indicators
        return "long"

    @property
    def entry_logic_text(self) -> str:
        """Convert entry conditions to text for backward compatibility"""
        if not self.entry_conditions:
            return "No conditions"

        condition_texts = []
        for condition in self.entry_conditions:
            condition_type = condition.get('condition', 'unknown')
            if condition_type == 'close_above_high':
                condition_texts.append('current_close > previous_high')
            elif condition_type == 'close_below_low':
                condition_texts.append('current_close < previous_low')
            elif condition_type == 'range_expansion':
                threshold = condition.get('threshold', 1.5)
                condition_texts.append(f'current_range > previous_range * {threshold}')
            else:
                condition_texts.append(f'{condition_type}')

        return f" {self.entry_logic} ".join(condition_texts)

    @property
    def stop_logic_text(self) -> str:
        """Convert exit conditions to stop logic text for backward compatibility"""
        for condition in self.exit_conditions:
            if condition['condition'] == 'fixed_stop_loss':
                percentage = condition.get('percentage', 0.02)
                return f'{percentage * 100}% stop loss'
            elif condition['condition'] == 'risk_reward_ratio':
                return 'previous_low'
        return 'previous_low'

    @property
    def target_logic_text(self) -> str:
        """Convert exit conditions to target logic text for backward compatibility"""
        for condition in self.exit_conditions:
            if condition['condition'] == 'fixed_take_profit':
                percentage = condition.get('percentage', 0.06)
                return f'{percentage * 100}% take profit'
            elif condition['condition'] == 'risk_reward_ratio':
                reward = condition.get('reward', 3)
                return f'entry_price + (entry_price - stop_price) * {reward}'
        return 'entry_price + (entry_price - stop_price) * 2.0'

    @property
    def position_size(self) -> float:
        """Extract position size for backward compatibility"""
        return self.position_sizing.get('value', 0.02) * 100  # Convert to percentage

    @property
    def timeframe(self) -> str:
        """Extract timeframe for backward compatibility"""
        optimal = self.optimal_conditions.get('timeframes', ['5m'])
        return optimal[0] if optimal else '5m'


class SchemaBasedPatternParser:
    """Schema-based parser that handles structured JSON patterns from LLM"""
    
    def __init__(self):
        self.patterns = []
        self.validation_errors = []
        self.supported_conditions = {
            # Price Action Conditions
            'range_contraction': self._range_contraction,
            'range_expansion': self._range_expansion,
            'inside_day': self._inside_day,
            'outside_day': self._outside_day,
            'gap_up': self._gap_up,
            'gap_down': self._gap_down,
            'higher_high': self._higher_high,
            'lower_low': self._lower_low,
            'breakout_above': self._breakout_above,
            'breakdown_below': self._breakdown_below,
            'close_above_high': self._close_above_high,
            'close_below_high': self._close_below_high,
            'close_below_low': self._close_below_low,

            # Volatility Conditions
            'low_volatility_regime': self._low_volatility_regime,
            'high_volatility_regime': self._high_volatility_regime,
            'volatility_expansion': self._volatility_expansion,
            'volatility_compression': self._volatility_compression,

            # Time-based Conditions
            'consecutive_days': self._consecutive_days,
            'day_of_week': self._day_of_week,
            'hour_of_day': self._hour_of_day,

            # Session/Filter Conditions
            'session_filter': self._session_filter,
            'time_filter': self._time_filter,
            'day_filter': self._day_filter,
            'trend_filter': self._trend_filter,
            'volume_filter': self._volume_filter,

            # Geometric Patterns
            'measured_move': self._measured_move,
            'retracement': self._retracement,
            'trend_continuation': self._trend_continuation,
            'trend_reversal': self._trend_reversal,
        }
        
        self.supported_exits = {
            'fixed_stop_loss': self._fixed_stop_loss,
            'fixed_take_profit': self._fixed_take_profit,
            'trailing_stop': self._trailing_stop,
            'risk_reward_ratio': self._risk_reward_ratio,
            'time_exit': self._time_exit,
            'pattern_failure': self._pattern_failure,
        }
    
    def parse_llm_response(self, llm_response: str) -> List[TradingPattern]:
        """Parse LLM JSON response into structured patterns"""
        self.patterns = []
        self.validation_errors = []
        
        try:
            # Try to parse as JSON first
            if llm_response.strip().startswith('{') or llm_response.strip().startswith('['):
                pattern_data = json.loads(llm_response)
                
                # Handle single pattern or array of patterns
                if isinstance(pattern_data, dict):
                    pattern_data = [pattern_data]
                
                for i, data in enumerate(pattern_data):
                    try:
                        pattern = self._create_pattern_from_json(data)
                        self.patterns.append(pattern)
                    except Exception as e:
                        self.validation_errors.append(f"Pattern {i+1} creation failed: {str(e)}")
            
            else:
                # Fallback: try to extract JSON from text response
                json_patterns = self._extract_json_from_text(llm_response)
                for i, json_str in enumerate(json_patterns):
                    try:
                        data = json.loads(json_str)
                        pattern = self._create_pattern_from_json(data)
                        self.patterns.append(pattern)
                    except Exception as e:
                        self.validation_errors.append(f"Pattern {i+1} JSON parsing failed: {str(e)}")
        
        except json.JSONDecodeError as e:
            raise BacktestingRuleParseError(f"UNBREAKABLE RULE VIOLATION: LLM must provide valid JSON format. Error: {e}")
        
        if not self.patterns:
            raise BacktestingRuleParseError("UNBREAKABLE RULE VIOLATION: No fallback allowed. LLM must provide properly formatted patterns.")
        
        return self.patterns
    
    def _extract_json_from_text(self, text: str) -> List[str]:
        """Extract JSON objects from text response"""
        json_objects = []
        
        # Look for JSON objects in the text
        json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        matches = re.findall(json_pattern, text, re.DOTALL)
        
        for match in matches:
            # Try to validate it's proper JSON
            try:
                json.loads(match)
                json_objects.append(match)
            except json.JSONDecodeError:
                continue
        
        return json_objects
    
    def _create_pattern_from_json(self, data: Dict[str, Any]) -> TradingPattern:
        """Create TradingPattern from JSON data"""
        # Validate required fields
        required_fields = ['pattern_name', 'entry_conditions', 'exit_conditions']
        for field in required_fields:
            if field not in data:
                raise ValueError(f"Missing required field: {field}")
        
        return TradingPattern(
            pattern_name=data['pattern_name'],
            entry_conditions=data['entry_conditions'],
            exit_conditions=data['exit_conditions'],
            entry_logic=data.get('entry_logic', 'AND'),
            filters=data.get('filters', []),
            position_sizing=data.get('position_sizing', {"method": "fixed_percent", "value": config.DEFAULT_POSITION_SIZE_PCT / 100}),
            description=data.get('description', ''),
            market_situation=data.get('market_situation', ''),
            behavioral_logic=data.get('behavioral_logic', ''),
            statistical_edge=data.get('statistical_edge', {}),
            optimal_conditions=data.get('optimal_conditions', {}),
            implementation_notes=data.get('implementation_notes', '')
        )
    
    def generate_python_functions(self) -> List[Callable]:
        """Generate Python functions for backtesting from patterns"""
        functions = []
        
        for pattern in self.patterns:
            func = self._create_python_function(pattern)
            if func:
                functions.append(func)
        
        return functions
    
    def _create_python_function(self, pattern: TradingPattern) -> Optional[Callable]:
        """Create a Python function from a trading pattern"""
        
        def pattern_function(data, current_idx):
            """Generated pattern function"""
            if current_idx < 1 or current_idx >= len(data):
                return None
            
            current = data.iloc[current_idx]
            previous = data.iloc[current_idx - 1]
            
            # Check filters first
            for filter_condition in pattern.filters:
                if not self._evaluate_condition(filter_condition, data, current_idx):
                    return None
            
            # Evaluate entry conditions
            entry_results = []
            for condition in pattern.entry_conditions:
                result = self._evaluate_condition(condition, data, current_idx)
                entry_results.append(result)
            
            # Apply entry logic
            if pattern.entry_logic == 'AND':
                entry_signal = all(entry_results)
            elif pattern.entry_logic == 'OR':
                entry_signal = any(entry_results)
            else:
                entry_signal = all(entry_results)  # Default to AND
            
            if not entry_signal:
                return None
            
            # Determine trade direction (default to long for now)
            direction = 'long'  # Can be enhanced to detect from conditions
            
            # Calculate prices
            entry_price = current['Close']
            stop_price = self._calculate_stop_price(pattern.exit_conditions, entry_price, current, previous, direction)
            target_price = self._calculate_target_price(pattern.exit_conditions, entry_price, stop_price, direction)
            
            if stop_price is None or target_price is None:
                return None
            
            # Validate order logic
            if direction == 'long':
                if stop_price >= entry_price or target_price <= entry_price:
                    return None
            else:  # short
                if stop_price <= entry_price or target_price >= entry_price:
                    return None
            
            # Calculate position size
            position_size = self._calculate_position_size(pattern.position_sizing)
            
            return {
                'entry_price': entry_price,
                'stop_loss': stop_price,
                'take_profit': target_price,
                'direction': direction,
                'position_size': position_size,
                'rule_id': hash(pattern.pattern_name) % 10000  # Generate ID from name
            }
        
        return pattern_function
    
    def _evaluate_condition(self, condition: Dict[str, Any], data: pd.DataFrame, current_idx: int) -> bool:
        """Evaluate a single condition - UNBREAKABLE RULE: NO FALLBACKS"""
        condition_type = condition['condition']

        if condition_type in self.supported_conditions:
            # UNBREAKABLE RULE: Let BacktestingRuleParseError propagate - NO FALLBACKS!
            try:
                result = self.supported_conditions[condition_type](data, current_idx, condition)
                # Debug logging for condition evaluation
                if hasattr(self, '_debug_logging') and self._debug_logging:
                    print(f"      🔧 Condition '{condition_type}' at idx {current_idx}: {result}")
                return result
            except BacktestingRuleParseError:
                # UNBREAKABLE RULE: Re-raise BacktestingRuleParseError - NO FALLBACKS!
                raise
            except Exception as e:
                # Only catch other exceptions (programming errors, etc.)
                raise BacktestingRuleParseError(
                    f"UNBREAKABLE RULE VIOLATION: Condition '{condition_type}' failed with error: {e}. "
                    "System must fail rather than use fallbacks."
                )
        else:
            raise BacktestingRuleParseError(
                f"UNBREAKABLE RULE VIOLATION: Unsupported condition type '{condition_type}'. "
                f"Available conditions: {list(self.supported_conditions.keys())}. "
                "System must fail rather than use fallbacks."
            )
    
    def _calculate_position_size(self, sizing: Dict[str, Any]) -> float:
        """Calculate position size based on pattern specifications - PROPER CAPPING"""
        method = sizing.get('method', 'fixed_percent')
        value = sizing.get('value', config.DEFAULT_POSITION_SIZE_PCT / 100)

        if method == 'fixed_percent':
            # PROPER DESIGN: Cap at maximum without spamming warnings
            max_size = config.MAX_POSITION_SIZE_PCT / 100
            min_size = config.MIN_POSITION_SIZE_PCT / 100

            # Silently cap the position size at the configured maximum
            # LLM should learn to optimize within these bounds
            capped_value = min(max_size, max(min_size, float(value)))

            # Only warn if this is a significant reduction (>50% reduction) and only once per pattern
            if float(value) > max_size and float(value) > max_size * 1.5:
                if not hasattr(self, '_position_size_warning_shown'):
                    print(f"      ℹ️ Position size capped at {max_size*100:.1f}% (max allowed)")
                    self._position_size_warning_shown = True

            return capped_value
        elif method == 'fixed_amount':
            # For fixed amounts, use absolute units (≥1)
            return max(1.0, float(value))
        else:
            return config.DEFAULT_POSITION_SIZE_PCT / 100  # Default from configuration
    
    def _calculate_stop_price(self, exit_conditions: List[Dict], entry_price: float,
                            current: pd.Series, previous: pd.Series, direction: str) -> Optional[float]:  # pylint: disable=unused-argument
        """Calculate stop loss price from exit conditions"""
        for condition in exit_conditions:
            if condition['condition'] == 'fixed_stop_loss':
                percentage = condition.get('percentage', config.FALLBACK_STOP_LOSS_PERCENTAGE / 100)
                if direction == 'long':
                    return entry_price * (1 - percentage)
                else:
                    return entry_price * (1 + percentage)
            elif condition['condition'] == 'risk_reward_ratio':
                # Use previous low/high as stop
                if direction == 'long':
                    return previous['Low']
                else:
                    return previous['High']
        
        # Default stop
        if direction == 'long':
            return previous['Low']
        else:
            return previous['High']
    
    def _calculate_target_price(self, exit_conditions: List[Dict], entry_price: float, 
                              stop_price: Optional[float], direction: str) -> Optional[float]:
        """Calculate target price from exit conditions"""
        for condition in exit_conditions:
            if condition['condition'] == 'fixed_take_profit':
                percentage = condition.get('percentage', config.FALLBACK_TAKE_PROFIT_PERCENTAGE / 100)
                if direction == 'long':
                    return entry_price * (1 + percentage)
                else:
                    return entry_price * (1 - percentage)
            elif condition['condition'] == 'risk_reward_ratio' and stop_price is not None:
                # LLM should provide the optimal reward ratio - only use fallback if LLM completely failed
                reward = condition.get('reward')
                if reward is None:
                    # Emergency fallback - this should rarely happen if LLM is working properly
                    reward = config.FALLBACK_RISK_REWARD_RATIO

                # Ensure minimum multiplier for profitability
                reward = max(reward, WALKFORWARD_MIN_MULTIPLIER)
                
                if direction == 'long':
                    return entry_price + (entry_price - stop_price) * reward
                else:
                    return entry_price - (stop_price - entry_price) * reward
        
        # Default target (2:1 risk-reward)
        if stop_price is not None:
            if direction == 'long':
                return entry_price + (entry_price - stop_price) * config.FALLBACK_RISK_REWARD_RATIO
            else:
                return entry_price - (stop_price - entry_price) * config.FALLBACK_RISK_REWARD_RATIO
        
        return None

    # Condition Implementation Methods
    def _range_contraction(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for range contraction over specified periods - FIXED: More restrictive threshold"""
        periods = params.get('periods', 4)
        threshold = params.get('threshold', config.RANGE_CONTRACTION_THRESHOLD)  # Configurable contraction threshold

        if current_idx < periods:
            return False

        ranges = data['High'] - data['Low']
        contractions_found = 0

        for i in range(1, periods + 1):
            if current_idx - i < 0:
                return False

            current_range = ranges.iloc[current_idx - i + 1]
            previous_range = ranges.iloc[current_idx - i]

            if previous_range == 0:
                continue

            contraction = (previous_range - current_range) / previous_range

            # Debug logging removed for cleaner output

            if contraction >= threshold:
                contractions_found += 1

        # FIXED: Require at least 2 out of 'periods' contractions to be more selective
        required_contractions = max(2, periods // 2)
        return contractions_found >= required_contractions

    def _range_expansion(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for range expansion - FIXED: More restrictive threshold"""
        threshold = params.get('threshold', config.RANGE_EXPANSION_THRESHOLD)  # Configurable expansion threshold
        lookback = params.get('lookback', config.DEFAULT_LOOKBACK_PERIODS)  # Configurable lookback period

        if current_idx < lookback:
            return False

        current_range = data['High'].iloc[current_idx] - data['Low'].iloc[current_idx]
        avg_range = (data['High'].iloc[current_idx-lookback:current_idx] -
                    data['Low'].iloc[current_idx-lookback:current_idx]).mean()

        if avg_range == 0:
            return False

        expansion_ratio = current_range / avg_range

        # Debug logging removed for cleaner output

        return expansion_ratio > threshold

    def _close_above_high(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check if current close is above previous high"""
        _ = params  # Unused parameter for interface consistency
        if current_idx < 1:
            return False

        current_close = data['Close'].iloc[current_idx]
        previous_high = data['High'].iloc[current_idx - 1]

        return current_close > previous_high

    def _close_below_high(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check if current close is below previous high"""
        _ = params  # Unused parameter for interface consistency
        if current_idx < 1:
            return False

        current_close = data['Close'].iloc[current_idx]
        previous_high = data['High'].iloc[current_idx - 1]

        return current_close < previous_high

    def _close_below_low(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check if current close is below previous low"""
        _ = params  # Unused parameter for interface consistency
        if current_idx < 1:
            return False

        current_close = data['Close'].iloc[current_idx]
        previous_low = data['Low'].iloc[current_idx - 1]

        return current_close < previous_low

    def _inside_day(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for inside day pattern"""
        _ = params  # Unused parameter for interface consistency
        if current_idx < 1:
            return False

        current_high = data['High'].iloc[current_idx]
        current_low = data['Low'].iloc[current_idx]
        previous_high = data['High'].iloc[current_idx - 1]
        previous_low = data['Low'].iloc[current_idx - 1]

        return current_high < previous_high and current_low > previous_low

    def _outside_day(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for outside day pattern"""
        _ = params  # Unused parameter for interface consistency
        if current_idx < 1:
            return False

        current_high = data['High'].iloc[current_idx]
        current_low = data['Low'].iloc[current_idx]
        previous_high = data['High'].iloc[current_idx - 1]
        previous_low = data['Low'].iloc[current_idx - 1]

        return current_high > previous_high and current_low < previous_low

    def _low_volatility_regime(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check if in low volatility regime - FIXED: More restrictive threshold"""
        lookback = params.get('lookback', config.DEFAULT_LOOKBACK_PERIODS)
        threshold = params.get('threshold', config.LOW_VOLATILITY_THRESHOLD)  # Configurable low volatility threshold

        if current_idx < lookback:
            return False

        returns = data['Close'].iloc[current_idx-lookback:current_idx+1].pct_change().dropna()
        volatility = returns.std()

        # Debug logging removed for cleaner output

        return volatility < threshold

    def _volatility_expansion(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for volatility expansion"""
        return self._range_expansion(data, current_idx, params)

    def _consecutive_days(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for consecutive days meeting criteria"""
        criteria_type = params.get('type', 'range_contraction')
        periods = params.get('periods', 3)

        if current_idx < periods:
            return False

        # Delegate to the specific criteria type
        if criteria_type == 'range_contraction':
            return self._range_contraction(data, current_idx, params)
        elif criteria_type == 'higher_high':
            # Check for consecutive higher highs
            for i in range(periods - 1):
                if data['High'].iloc[current_idx - i] <= data['High'].iloc[current_idx - i - 1]:
                    return False
            return True
        elif criteria_type == 'inside_day':
            # Check for consecutive inside days
            for i in range(periods):
                if not self._inside_day(data, current_idx - i, {}):
                    return False
            return True
        else:
            return False

    # Implemented condition methods
    def _gap_up(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for gap up"""
        if current_idx < 1:
            return False

        lookback = params.get('lookback', 1)
        if current_idx < lookback:
            return False

        current_low = data['Low'].iloc[current_idx]
        previous_high = data['High'].iloc[current_idx - lookback]

        # Gap up: current low is above previous high
        return current_low > previous_high

    def _gap_down(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for gap down"""
        if current_idx < 1:
            return False

        lookback = params.get('lookback', 1)
        if current_idx < lookback:
            return False

        current_high = data['High'].iloc[current_idx]
        previous_low = data['Low'].iloc[current_idx - lookback]

        # Gap down: current high is below previous low
        return current_high < previous_low

    def _higher_high(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for higher high"""
        _ = params  # Unused parameter for interface consistency
        if current_idx < 1:
            return False

        current_high = data['High'].iloc[current_idx]
        previous_high = data['High'].iloc[current_idx - 1]

        return current_high > previous_high

    def _lower_low(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Check for lower low"""
        _ = params  # Unused parameter for interface consistency
        if current_idx < 1:
            return False

        current_low = data['Low'].iloc[current_idx]
        previous_low = data['Low'].iloc[current_idx - 1]

        return current_low < previous_low

    def _breakout_above(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:  # pylint: disable=unused-argument
        return False

    def _breakdown_below(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:  # pylint: disable=unused-argument
        return False

    def _high_volatility_regime(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        return not self._low_volatility_regime(data, current_idx, params)

    def _volatility_compression(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        return not self._volatility_expansion(data, current_idx, params)

    def _day_of_week(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        return False

    def _hour_of_day(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        return False

    def _measured_move(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        return False

    def _retracement(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        return False

    def _trend_continuation(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        return False

    def _trend_reversal(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        return False

    # ============================================================================
    # FILTER CONDITIONS (Session, Time, Volume, etc.)
    # ============================================================================

    def _session_filter(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Filter based on trading session - UNBREAKABLE RULE: NO FALLBACKS"""
        if current_idx < 0 or current_idx >= len(data):
            return False

        # Check if we have DateTime column
        if 'DateTime' not in data.columns and data.index.name != 'DateTime':
            raise BacktestingRuleParseError(
                "UNBREAKABLE RULE VIOLATION: session_filter requires DateTime column or DateTime index. "
                "Current data lacks timestamp information. System must fail rather than use fallbacks."
            )

        session_value = params.get('value', 'all').lower()

        # For basic implementation, accept common session values
        # In a full implementation, this would parse DateTime and check actual session times
        if session_value in ['all', 'any', 'overlap', 'london', 'ny', 'asian']:
            return True

        raise BacktestingRuleParseError(
            f"UNBREAKABLE RULE VIOLATION: Unsupported session_filter value '{session_value}'. "
            "Supported values: all, any, overlap, london, ny, asian. System must fail rather than use fallbacks."
        )

    def _time_filter(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Filter based on time of day - UNBREAKABLE RULE: NO FALLBACKS"""
        if current_idx < 0 or current_idx >= len(data):
            return False

        # Check if we have DateTime column
        if 'DateTime' not in data.columns and data.index.name != 'DateTime':
            raise BacktestingRuleParseError(
                "UNBREAKABLE RULE VIOLATION: time_filter requires DateTime column or DateTime index. "
                "Current data lacks timestamp information. System must fail rather than use fallbacks."
            )

        # For basic implementation, accept all times since we have DateTime but would need complex parsing
        # In a full implementation, this would parse the time and filter by hour ranges
        return True

    def _day_filter(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Filter based on day of week - UNBREAKABLE RULE: NO FALLBACKS"""
        if current_idx < 0 or current_idx >= len(data):
            return False

        # Check if we have DateTime column
        if 'DateTime' not in data.columns and data.index.name != 'DateTime':
            raise BacktestingRuleParseError(
                "UNBREAKABLE RULE VIOLATION: day_filter requires DateTime column or DateTime index. "
                "Current data lacks timestamp information. System must fail rather than use fallbacks."
            )

        # For basic implementation, accept all days since we have DateTime but would need complex parsing
        # In a full implementation, this would parse the date and filter by day of week
        return True

    def _trend_filter(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Filter based on overall trend direction"""
        if current_idx < 20:
            raise BacktestingRuleParseError(
                "UNBREAKABLE RULE VIOLATION: trend_filter requires at least 20 data points for calculation. "
                f"Current index {current_idx} is insufficient. System must fail rather than use fallbacks."
            )

        lookback = params.get('lookback', config.DEFAULT_LOOKBACK_PERIODS)
        threshold = params.get('threshold', config.HIGH_VOLATILITY_THRESHOLD)

        if current_idx < lookback:
            raise BacktestingRuleParseError(
                f"UNBREAKABLE RULE VIOLATION: trend_filter requires {lookback} lookback periods. "
                f"Current index {current_idx} is insufficient. System must fail rather than use fallbacks."
            )

        # Simple trend filter: check if price is above/below moving average
        ma = data['Close'].iloc[current_idx - lookback:current_idx].mean()
        current_price = data['Close'].iloc[current_idx]

        trend_strength = (current_price - ma) / ma
        return abs(trend_strength) > threshold

    def _volume_filter(self, data: pd.DataFrame, current_idx: int, params: Dict) -> bool:
        """Filter based on volume conditions - UNBREAKABLE RULE: NO FALLBACKS"""
        if 'Volume' not in data.columns:
            raise BacktestingRuleParseError(
                "UNBREAKABLE RULE VIOLATION: volume_filter requires Volume column in data. "
                "Current data lacks volume information. System must fail rather than use fallbacks. "
                "Either provide data with Volume column or remove volume_filter from pattern."
            )

        if current_idx < 1:
            raise BacktestingRuleParseError(
                "UNBREAKABLE RULE VIOLATION: volume_filter requires at least 1 previous data point. "
                f"Current index {current_idx} is insufficient. System must fail rather than use fallbacks."
            )

        threshold = params.get('threshold', config.MEASURED_MOVE_THRESHOLD)
        lookback = params.get('lookback', config.DEFAULT_LOOKBACK_PERIODS)

        if current_idx < lookback:
            raise BacktestingRuleParseError(
                f"UNBREAKABLE RULE VIOLATION: volume_filter requires {lookback} lookback periods. "
                f"Current index {current_idx} is insufficient. System must fail rather than use fallbacks."
            )

        avg_volume = data['Volume'].iloc[current_idx - lookback:current_idx].mean()
        current_volume = data['Volume'].iloc[current_idx]

        return current_volume > (avg_volume * threshold)

    # Exit condition methods (placeholders)
    def _fixed_stop_loss(self, data: pd.DataFrame, position, params: Dict) -> bool:
        return False

    def _fixed_take_profit(self, data: pd.DataFrame, position, params: Dict) -> bool:
        return False

    def _trailing_stop(self, data: pd.DataFrame, position, params: Dict) -> bool:
        return False

    def _risk_reward_ratio(self, data: pd.DataFrame, position, params: Dict) -> bool:
        return False

    def _time_exit(self, data: pd.DataFrame, position, params: Dict) -> bool:
        return False

    def _pattern_failure(self, data: pd.DataFrame, position, params: Dict) -> bool:
        return False

    def _extract_patterns(self, llm_response: str) -> List[str]:
        """Extract individual patterns for backward compatibility with cortex.py"""
        try:
            patterns = self.parse_llm_response(llm_response)
            # Return pattern names as strings for compatibility
            return [pattern.pattern_name for pattern in patterns]
        except Exception:
            # Fallback: return the whole response as a single pattern
            return [llm_response]


# Backward compatibility factory function
def BacktestingTradingRule(pattern_name=None, entry_logic_text=None, stop_logic_text=None,
                          target_logic_text=None, direction=None, position_size=None,
                          timeframe=None, rule_id=None, **kwargs):
    """Factory function for backward compatibility with old constructor signature"""

    # Convert old parameters to new schema format
    entry_conditions = []
    exit_conditions = []

    # Parse entry logic text to conditions
    if entry_logic_text:
        if "close > high" in entry_logic_text.lower():
            entry_conditions.append({"condition": "close_above_high", "lookback": 1})
        elif "close < low" in entry_logic_text.lower():
            entry_conditions.append({"condition": "close_below_low", "lookback": 1})
        else:
            entry_conditions.append({"condition": "custom", "description": entry_logic_text})

    # Parse exit logic to conditions
    if target_logic_text and "entry_price" in target_logic_text:
        # Extract risk-reward ratio if present
        if "*" in target_logic_text:
            try:
                parts = target_logic_text.split("*")
                if len(parts) > 1:
                    reward = float(parts[1].strip())
                    exit_conditions.append({"condition": "risk_reward_ratio", "risk": 1, "reward": reward})
            except:
                exit_conditions.append({"condition": "fixed_take_profit", "percentage": 0.03})
        else:
            exit_conditions.append({"condition": "fixed_take_profit", "percentage": 0.03})

    # Default conditions if none provided
    if not entry_conditions:
        entry_conditions = [{"condition": "close_above_high", "lookback": 1}]
    if not exit_conditions:
        exit_conditions = [{"condition": "risk_reward_ratio", "risk": 1, "reward": 2}]

    # Convert position size
    position_sizing = {"method": "fixed_percent", "value": (position_size or 2.0) / 100}

    # Filter out unsupported kwargs
    supported_kwargs = {}
    unsupported_params = ['name', 'rule_id', 'market_logic', 'entry_logic', 'stop_logic', 'target_logic']
    for key, value in kwargs.items():
        if key not in unsupported_params:  # Filter out old parameters
            supported_kwargs[key] = value

    # Create the new pattern
    return TradingPattern(
        pattern_name=pattern_name or "Legacy Pattern",
        entry_conditions=entry_conditions,
        exit_conditions=exit_conditions,
        position_sizing=position_sizing,
        optimal_conditions={"timeframes": [timeframe or "5m"]},
        **supported_kwargs
    )

# Backward compatibility aliases
BacktestingRuleParser = SchemaBasedPatternParser


# Backward compatibility functions
def parse_backtesting_rules(llm_response: str) -> List[Callable]:
    """Parse LLM response and return pattern functions using schema-based parsing"""
    parser = SchemaBasedPatternParser()
    try:
        parser.parse_llm_response(llm_response)
        return parser.generate_python_functions()
    except BacktestingRuleParseError as e:
        print(f"Schema-based pattern parsing failed: {e}")
        return []
