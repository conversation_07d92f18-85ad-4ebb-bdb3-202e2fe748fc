![<PERSON><PERSON><PERSON> Logo](../branding/jaeger-logo.png)

# ⚙️ Jaeger Configuration Guide

> **2025-06-26 Update:**
> - All configuration is now strictly enforced via `jaeger_config.env` (no hardcoded defaults)
> - System will fail fast if any required variable is missing
> - Audit completed, <PERSON><PERSON><PERSON> is now production ready


## 🎯 **DYNAMIC RISK MANAGEMENT SETTINGS (Revolutionary!)**

### **Core Dynamic Risk Settings:**
```python
# Enable/Disable the revolutionary LLM risk analysis
ENABLE_LLM_RISK_ANALYSIS = true

# User-Configurable Risk Boundaries (NOT hardcoded rules!)
MAX_RISK_PER_PATTERN = 5.0      # LLM can't exceed 5% risk per pattern
MIN_RISK_PER_PATTERN = 0.5      # LLM can't go below 0.5% risk per pattern
MAX_TOTAL_PORTFOLIO_RISK = 15.0  # All patterns combined can't exceed 15%
```

### **Risk Profile Examples:**

#### **Conservative Trader:**
```python
MAX_RISK_PER_PATTERN = 2.0      # Maximum 2% per pattern
MIN_RISK_PER_PATTERN = 0.3      # Minimum 0.3% per pattern
MAX_TOTAL_PORTFOLIO_RISK = 8.0   # Total 8% portfolio risk
```

#### **Aggressive Trader:**
```python
MAX_RISK_PER_PATTERN = 8.0      # Maximum 8% per pattern
MIN_RISK_PER_PATTERN = 1.0      # Minimum 1% per pattern
MAX_TOTAL_PORTFOLIO_RISK = 25.0  # Total 25% portfolio risk
```

#### **Balanced Trader (Default):**
```python
MAX_RISK_PER_PATTERN = 5.0      # Maximum 5% per pattern
MIN_RISK_PER_PATTERN = 0.5      # Minimum 0.5% per pattern
MAX_TOTAL_PORTFOLIO_RISK = 15.0  # Total 15% portfolio risk
```

---

**Complete guide to configuring the LLM Pattern Discovery Trading System**

## 🎯 Overview

Jaeger now includes a centralized configuration system that allows you to customize all aspects of the system without modifying code. This makes it easy to:

- Adjust LLM parameters for different models
- Modify trading risk parameters
- Configure data processing settings
- Customize file paths and directories
- Enable/disable features

## 📁 Configuration Files

### 1. **`config.py`** - Core Configuration System
- Contains all default settings
- Handles environment variable loading
- Provides type conversion and validation
- **DO NOT MODIFY** unless you understand Python

### 2. **`jaeger_config.env`** - User Configuration (Optional)
- Override any default setting
- Simple key=value format
- Automatically loaded if present
- Safe to modify and share

## ⚙️ Configuration Categories

### **🤖 LLM Settings**
```env
# LM Studio connection
LM_STUDIO_URL=http://localhost:1234
LM_STUDIO_TIMEOUT=600

# Model selection mode
LM_STUDIO_MODEL_SELECTION_MODE=auto  # Options: 'auto' or 'manual'

# LLM behavior
LLM_TEMPERATURE=0.1
LLM_MAX_TOKENS=2500
```

#### **Model Selection Configuration**

**Automatic Model Selection (`auto`):**
- System uses the configured default model from `LM_STUDIO_DEFAULT_MODEL`
- FAIL HARD if configured model not available - NO FALLBACKS
- Filters out embedding models and duplicate instances
- Best for automated workflows and production environments

**Manual Model Selection (`manual`):**
- Interactive prompt allows you to choose from available models
- Shows only unique chat models (no duplicates)
- Ideal for development and testing different models

**Configuration:**
```bash
# In jaeger_config.env
LM_STUDIO_MODEL_SELECTION_MODE=manual  # Default: manual for user control
LM_STUDIO_DEFAULT_MODEL=meta-llama-3.1-8b-instruct  # Required for auto mode
```

**Important Notes:**
- **Manual Mode (Default)**: Recommended for development and experimentation
- **Auto Mode**: Only use when you want automated operation with a specific model
- **No Fallbacks**: System will fail hard if configured model unavailable in auto mode
- **Multiple Models**: Available for experimentation purposes, never as fallbacks

**Command Line Override:**
```bash
# Force auto mode regardless of config setting
python src/ai_integration/lm_studio_client.py --auto

# Use config setting (manual or auto)
python src/ai_integration/lm_studio_client.py
```

**Duplicate Instance Filtering:**
Jaeger automatically filters out duplicate model instances (e.g., `meta-llama-3.1-8b-instruct:2`) to ensure only one instance per model is loaded. This prevents resource conflicts and ensures consistent behavior.

### **📊 Data Processing**
```env
# Data validation
MIN_RECORDS_REQUIRED=100
DATA_VALIDATION_ENABLED=true
MAX_MEMORY_USAGE_MB=2048
```

### **💰 Trading Parameters**
```env
# Risk management
DEFAULT_STOP_LOSS_PCT=0.8
DEFAULT_TAKE_PROFIT_PCT=1.2
MAX_HOLDING_MINUTES=180
MIN_RISK_THRESHOLD=0.000001

# Professional backtesting.py settings
BACKTEST_CASH=10000
BACKTEST_SPREAD=0.0002
BACKTEST_COMMISSION=0.0
BACKTEST_MARGIN=0.02
RISK_MULTIPLE=3.0
MAX_R_MULTIPLE=10.0
MIN_R_MULTIPLE=-10.0

# Position sizing and trade limits (USER CONFIGURABLE)
MAX_POSITION_SIZE_PCT=1.0
MIN_POSITION_SIZE_PCT=0.5
DEFAULT_POSITION_SIZE_PCT=1.0
MAX_CONCURRENT_TRADES=3
POSITION_SIZE_REDUCTION_THRESHOLD=2.0

# Pattern condition thresholds (USER CONFIGURABLE)
RANGE_CONTRACTION_THRESHOLD=0.30
RANGE_EXPANSION_THRESHOLD=2.0
LOW_VOLATILITY_THRESHOLD=0.002
HIGH_VOLATILITY_THRESHOLD=0.01
MEASURED_MOVE_THRESHOLD=1.5
DEFAULT_LOOKBACK_PERIODS=20

# Default risk/reward ratios (USER CONFIGURABLE)
DEFAULT_RISK_REWARD_RATIO=2.0
DEFAULT_STOP_LOSS_PERCENTAGE=2.0
DEFAULT_TAKE_PROFIT_PERCENTAGE=6.0

# LLM and validation settings (USER CONFIGURABLE)
LLM_TRANSLATION_TEMPERATURE=0.3
VALIDATOR_MAX_RETRIES=2
VALIDATOR_RETRY_DELAY=1.0
QUALITY_SCORE_EXCELLENT_THRESHOLD=0.85
QUALITY_SCORE_GOOD_THRESHOLD=0.70
QUALITY_SCORE_FAIR_THRESHOLD=0.50
```

### **📁 File Paths**
```env
# Directories
DATA_DIR=data
RESULTS_DIR=results
LOG_FILE=jaeger.log
```

### **🤖 MT4 Settings**
```env
# Expert Advisor defaults
MT4_DEFAULT_LOT_SIZE=0.1
MT4_MAGIC_NUMBER=12345
MT4_SLIPPAGE=3
```

### **📝 Logging**
```env
# Logging configuration
LOG_LEVEL=INFO
LOG_TO_FILE=true
LOG_TO_CONSOLE=true
```

### **🧠 LLM Learning System (NEW!)**
```env
# Learning system configuration
LLM_LEARNING_ENABLED=true           # Enable cross-session learning
MAX_LEARNING_SESSIONS=100           # Keep last 100 sessions per symbol
LEARNING_DATA_DIR=llm_data          # Directory for learning data
SAVE_PROFITABLE_ONLY=true          # Only save feedback from profitable patterns
```

**🎯 Learning System Benefits:**
- **Symbol-Specific Learning**: Each trading symbol builds its own knowledge base
- **Automatic Management**: Old sessions cleaned up automatically
- **Performance Focus**: Only profitable patterns contribute to learning
- **Clean Organization**: Learning data separate from results

### **🎯 TRUE Dynamic Pattern Discovery**
```env
# TRUE dynamic profitability criteria (NO hardcoded values)
ENABLE_DYNAMIC_CRITERIA=true         # Enable TRUE dynamic criteria
```

**🚀 TRUE Dynamic Approach:**
- **Simple Rule**: If total PnL > 0, pattern is accepted
- **No Hardcoded Thresholds**: Zero fixed values or "quality floors"
- **Any Profitable Combination**: Any pattern that makes money is accepted
- **Maximum Flexibility**: Works with any instrument, timeframe, or market condition
- **Highest Success Rate**: Will find profitable patterns in any market environment

## 🔧 How to Configure

### **Method 1: Environment File (Recommended)**

1. Create `jaeger_config.env` in the main Jaeger directory
2. Add your settings:
```env
# My custom Jaeger configuration
LLM_TEMPERATURE=0.2
MAX_HOLDING_MINUTES=240
DEFAULT_STOP_LOSS_PCT=1.0
LOG_LEVEL=DEBUG
```
3. Run Jaeger normally - settings will be automatically loaded

### **Method 2: Environment Variables**
Set environment variables before running:
```bash
export LLM_TEMPERATURE=0.2
export MAX_HOLDING_MINUTES=240
./run_jaeger.command
```

### **Method 3: Programmatic (Advanced)**
```python
from config import config
config.LLM_TEMPERATURE = 0.2
config.MAX_HOLDING_MINUTES = 240
```

## 🎛️ User-Configurable Trading Parameters

### **📊 Position Sizing Controls**
```env
# Maximum position size as percentage of equity
MAX_POSITION_SIZE_PCT=1.0          # 1% maximum position size

# Minimum position size as percentage of equity
MIN_POSITION_SIZE_PCT=0.5          # 0.5% minimum position size

# Default position size when LLM doesn't specify
DEFAULT_POSITION_SIZE_PCT=1.0      # 1% default position size

# Maximum number of concurrent open trades
MAX_CONCURRENT_TRADES=3            # Limit to 3 simultaneous positions

# Threshold for automatic position size reduction
POSITION_SIZE_REDUCTION_THRESHOLD=2.0  # Reduce if LLM suggests >2%
```

### **🎯 Pattern Detection Thresholds**
```env
# Range contraction detection sensitivity
RANGE_CONTRACTION_THRESHOLD=0.30   # 30% range contraction required

# Range expansion detection sensitivity
RANGE_EXPANSION_THRESHOLD=2.0      # 2x average range for expansion

# Volatility regime thresholds
LOW_VOLATILITY_THRESHOLD=0.002     # 0.2% for low volatility
HIGH_VOLATILITY_THRESHOLD=0.01     # 1% for high volatility

# Measured move pattern threshold
MEASURED_MOVE_THRESHOLD=1.5        # 1.5x initial move

# Default lookback periods for calculations
DEFAULT_LOOKBACK_PERIODS=20        # 20 bars for averages
```

### **💰 Risk/Reward Configuration**
```env
# Default risk-reward ratio when not specified
DEFAULT_RISK_REWARD_RATIO=2.0      # 2:1 reward-to-risk ratio

# Default stop loss percentage
DEFAULT_STOP_LOSS_PERCENTAGE=2.0   # 2% stop loss

# Default take profit percentage
DEFAULT_TAKE_PROFIT_PERCENTAGE=6.0 # 6% take profit
```

### **🤖 LLM and Validation Settings**
```env
# Temperature for LLM pattern translation
LLM_TRANSLATION_TEMPERATURE=0.3    # Lower = more precise

# Validation retry settings
VALIDATOR_MAX_RETRIES=2            # Maximum validation attempts
VALIDATOR_RETRY_DELAY=1.0          # Seconds between retries

# Quality score thresholds
QUALITY_SCORE_EXCELLENT_THRESHOLD=0.85  # 85% for excellent
QUALITY_SCORE_GOOD_THRESHOLD=0.70       # 70% for good
QUALITY_SCORE_FAIR_THRESHOLD=0.50       # 50% for fair
```

## 📋 Common Configuration Scenarios

### **🎯 Conservative Trading**
```env
# Conservative position sizing
MAX_POSITION_SIZE_PCT=0.5
DEFAULT_POSITION_SIZE_PCT=0.5
MAX_CONCURRENT_TRADES=2

# Conservative risk management
DEFAULT_STOP_LOSS_PCT=0.5
DEFAULT_TAKE_PROFIT_PCT=1.0
DEFAULT_RISK_REWARD_RATIO=2.0

# Conservative pattern detection
RANGE_CONTRACTION_THRESHOLD=0.40
LOW_VOLATILITY_THRESHOLD=0.001
```

### **🚀 Aggressive Trading**
```env
# Aggressive position sizing
MAX_POSITION_SIZE_PCT=2.0
DEFAULT_POSITION_SIZE_PCT=1.5
MAX_CONCURRENT_TRADES=5

# Aggressive risk management
DEFAULT_STOP_LOSS_PCT=1.5
DEFAULT_TAKE_PROFIT_PCT=3.0
DEFAULT_RISK_REWARD_RATIO=3.0

# Aggressive pattern detection
RANGE_CONTRACTION_THRESHOLD=0.20
RANGE_EXPANSION_THRESHOLD=1.5
```

### **⚡ High-Frequency Setup**
```env
# Smaller positions, more trades
MAX_POSITION_SIZE_PCT=0.3
DEFAULT_POSITION_SIZE_PCT=0.2
MAX_CONCURRENT_TRADES=10

# Tighter risk management
DEFAULT_RISK_REWARD_RATIO=1.5
DEFAULT_LOOKBACK_PERIODS=10

# More sensitive pattern detection
RANGE_CONTRACTION_THRESHOLD=0.15
LOW_VOLATILITY_THRESHOLD=0.0005
```

### **🧪 Development/Testing**
```env
LOG_LEVEL=DEBUG
MIN_RECORDS_REQUIRED=50
LLM_TEMPERATURE=0.3
DATA_VALIDATION_ENABLED=true
```

### **🚨 Testing Standards (MANDATORY)**
```env
# UNBREAKABLE RULE: REAL DATA ONLY
# Never use synthetic/mock data for testing
REAL_DATA_ONLY=true
TEST_DATA_SOURCE=tests/RealTestData/
SYNTHETIC_DATA_FORBIDDEN=true
```

### **🏭 Production**
```env
LOG_LEVEL=INFO
LOG_TO_CONSOLE=false
MIN_RECORDS_REQUIRED=1000
LLM_TEMPERATURE=0.1
```

## 🔍 Configuration Validation

The system automatically validates all configuration values:

- **Type checking**: Ensures numbers are numbers, booleans are booleans
- **Range validation**: Prevents invalid values (e.g., negative percentages)
- **File path validation**: Checks directory existence
- **Dependency validation**: Ensures compatible settings

## 🚨 Troubleshooting

### **Configuration Not Loading**
- Check file name: `jaeger_config.env` (not `.txt`)
- Check file location: Same directory as `run_jaeger.command`
- Check syntax: `KEY=VALUE` (no spaces around `=`)

### **Invalid Values**
- Check logs for validation errors
- Ensure numeric values don't have units (use `0.8` not `0.8%`)
- Boolean values: `true`/`false` (lowercase)

### **Performance Issues**
- Reduce `LLM_MAX_TOKENS` for faster responses
- Increase `MIN_RECORDS_REQUIRED` for more reliable patterns
- Adjust `MAX_MEMORY_USAGE_MB` based on available RAM

## 📖 Advanced Configuration

### **Custom Data Directories**
```env
DATA_DIR=/path/to/my/market/data
RESULTS_DIR=/path/to/my/results
```

### **Multiple LM Studio Instances**
```env
LM_STUDIO_URL=http://*************:1234
LM_STUDIO_TIMEOUT=1200
```

### **High-Frequency Trading Setup**
```env
MAX_HOLDING_MINUTES=30
MIN_RISK_THRESHOLD=0.0001
DEFAULT_STOP_LOSS_PCT=0.3
DEFAULT_TAKE_PROFIT_PCT=0.6
```

## 🔒 Security Notes

- Configuration files may contain sensitive paths
- Don't share configuration files with absolute paths
- Use relative paths when possible
- Keep LM Studio URL internal to your network

## 📚 Reference

For complete list of all configuration options, see `config.py` or run:
```python
from config import config
print(config.to_dict())
```

---

**🎯 The configuration system makes Jaeger adaptable to any trading style or technical setup while maintaining the simplicity of double-click operation.**
